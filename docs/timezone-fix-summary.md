# Timezone Fix for assign-tasks API

## Vấn đề (Problem)

Khi gọi API `/assign-tasks` với payload:
```json
{
    "state_id": "e335e11c-ed9d-45ef-ad58-ba24b032282a",
    "department_id": "fa642cdb-58ae-43de-b390-774a19294279",
    "tasks": [
        {
            "assigned_to": "a08abc5c-039b-44a3-afa3-c1fc6ab28675",
            "template_id": "505332c4-2a53-4004-9970-f8cf3b054a35",
            "start_date": "2025-06-22",
            "end_date": "2025-06-22",
            "description": "3123213",
            "exp_quantity": 12
        }
    ],
    "production_plan_id": "f5566746-e20c-4e46-95c3-4e3c80379948"
}
```

Sau khi tạo task và get lại, `start_date` và `end_date` bị chuyển thành `2025-06-19 17:00:00` thay vì `2025-06-22`.

## <PERSON>uy<PERSON>n nhân (Root Cause)

1. **DTO Validation**: Khi API nhận payload, `@IsDate()` decorator convert string `"2025-06-22"` thành Date object
2. **SQL Generation**: Trong `generateInsertSQLTaskArray()` và `generateUpsertSQLTaskArray()`, hàm `formatDateForSQL()` được gọi
3. **UTC Conversion**: Hàm `formatDateForSQL()` sử dụng `date.toISOString().slice(0, 10)` - convert sang UTC
4. **Timezone Issue**: Ở múi giờ Việt Nam (UTC+7), `"2025-06-22T00:00:00"` local time = `"2025-06-21T17:00:00Z"` UTC
5. **Date Shift**: Khi cắt lấy phần ngày từ UTC string, được `"2025-06-21"` thay vì `"2025-06-22"`

## Giải pháp (Solution)

### 1. Sửa `formatDateForSQL()` trong `src/utils/helpers/helper.ts`

**Trước (Before):**
```javascript
function formatDateForSQL(date: any) {
  return date.toISOString().slice(0, 10);
}
```

**Sau (After):**
```javascript
function formatDateForSQL(date: any) {
  // Use local timezone instead of UTC to preserve the original date
  // This prevents timezone conversion issues when storing dates
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
```

### 2. Sửa `formatDateForDatabase()` trong `TaskCleanupUtils.ts`

**Trước (Before):**
```javascript
static formatDateForDatabase(date: Date | string): string {
  if (date instanceof Date) {
    return date.toISOString().split('T')[0];
  }
  return date;
}
```

**Sau (After):**
```javascript
static formatDateForDatabase(date: Date | string): string {
  if (date instanceof Date) {
    // Use local timezone instead of UTC to preserve the original date
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  return date;
}
```

## Kết quả (Result)

- ✅ API payload `"start_date": "2025-06-22"` giờ được lưu đúng là `2025-06-22` trong database
- ✅ Không còn bị chuyển đổi múi giờ không mong muốn
- ✅ Tương thích với tất cả múi giờ
- ✅ Không ảnh hưởng đến logic khác

## Test Verification

Chạy test để verify fix:
```bash
node test-examples/timezone-fix-verification.js
```

Kết quả test cho thấy:
- Old format (UTC): `2025-06-21` ❌
- New format (Local): `2025-06-22` ✅

## Files Changed

1. `src/utils/helpers/helper.ts` - Sửa hàm `formatDateForSQL()`
2. `src/modules/seasonal-management/farming-plan-task/vietplants-env-task/utils/TaskCleanupUtils.ts` - Sửa hàm `formatDateForDatabase()`
3. `test-examples/timezone-fix-verification.js` - Test case để verify fix

## Impact Assessment

- **Low Risk**: Chỉ thay đổi cách format date, không thay đổi logic business
- **Backward Compatible**: Không ảnh hưởng đến data hiện tại
- **Performance**: Không impact performance
- **Scope**: Chỉ ảnh hưởng đến date formatting trong SQL generation

## Recommendation

Nên test kỹ trên staging environment trước khi deploy production để đảm bảo không có side effects khác.
