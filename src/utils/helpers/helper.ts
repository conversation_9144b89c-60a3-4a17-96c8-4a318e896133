import { ICurrentUser } from '@app/interfaces';
import { IIotExportDetail } from '@app/interfaces/IIotExportDetail';
import { IIotExportTransaction } from '@app/interfaces/IIotExportTransaction';
import { IIotImportDetail } from '@app/interfaces/IIotImportDetail';
import { IIotImportTransaction } from '@app/interfaces/IIotImportTransaction';
import { SqlQuery } from '@app/interfaces/ISQLQuery';
import { ERPExecute, ERPExecuteWithTransaction } from '@app/loaders/pglib/PGDB';
import moment from 'moment';
import { HttpError } from 'routing-controllers';
import { createSafeUOMDataForSQL } from './uom-helper';

const { v4: uuidv4 } = require('uuid');

// Hàm kiểm tra xem có giá trị không hợp lệ trong object hay không
export function getInvalidFields(data: any): { invalidFields: string[]; invalidValues: string[] } {
  const invalidFields: string[] = [];
  const invalidValues: string[] = [];

  // Đặc tả các kí tự không an toàn (unsafe characters) bạn muốn kiểm tra
  const unsafeCharacterPattern = /[!#$&'()*+,/:;=?@\[\]]/;

  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key];

      // Kiểm tra giá trị của mỗi trường có chứa kí tự không an toàn không
      if (typeof value === 'string' && unsafeCharacterPattern.test(value)) {
        invalidFields.push(key);
        invalidValues.push(value);
      }
    }
  }

  return { invalidFields, invalidValues };
}

export function areAllArraysEqual(arrays: any[]) {
  if (!arrays || arrays.length <= 1) {
    return true; // If there's only one or zero arrays, they are equal by default
  }

  const arraySets = arrays.map(array => new Set(array));

  for (let i = 1; i < arraySets.length; i++) {
    if (!areSetsEqual(arraySets[0], arraySets[i])) {
      return false;
    }
  }

  return true;
}

function areSetsEqual(set1: any, set2: any) {
  if (set1.size !== set2.size) {
    return false;
  }

  for (const element of set1) {
    if (!set2.has(element)) {
      return false;
    }
  }

  return true;
}

//////////////////////////////////////////////FOR CREATE TASK ARRAY/////////////////////////
function escapeSingleQuotes(str: string) {
  return str.replace(/'/g, "''");
}

function formatDateForSQL(date: any) {
  // Use local timezone instead of UTC to preserve the original date
  // This prevents timezone conversion issues when storing dates
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

function getObjectValues(obj: any) {
  return Object.values(obj).map(value => {
    if (value === null) {
      return 'NULL';
    } else if (typeof value === 'string') {
      return `'${escapeSingleQuotes(value)}'`;
    } else if (value instanceof Date) {
      return `'${formatDateForSQL(value)}'`;
    } else {
      return value;
    }
  });
}

// Define interfaces for type safety
interface Todo {
  name?: string;
  [key: string]: any;
}

interface Item {
  name?: string;
  [key: string]: any;
}

interface Worksheet {
  name?: string;
  [key: string]: any;
}

interface ProdQuantity {
  name?: string;
  [key: string]: any;
}

interface InvolveUser {
  customer_user: string;
}

interface FarmingPlanTask {
  [key: string]: any;
  name?: string;
  todo_list?: Todo[];
  item_list?: Item[];
  worksheet_list?: Worksheet[];
  prod_quantity_list?: ProdQuantity[];
  involve_in_users?: InvolveUser[];
}

// Utility function to safely escape SQL values
function escapeSQLValue(value: any): string {
  if (value === null || value === undefined || value === '') {
    return 'NULL';
  }
  if (typeof value === 'string') {
    return `'${value.replace(/'/g, "''")}'`;
  }
  if (typeof value === 'boolean') {
    return value ? 'TRUE' : 'FALSE';
  }
  return value.toString();
}

// Utility function to get object values safely

/**
 * Generate upsert SQL queries for task array with INSERT ... ON CONFLICT DO UPDATE
 * Supports both creating new tasks and updating existing ones
 */
export function generateUpsertSQLTaskArray(
  dataArray: FarmingPlanTask[],
  originalArray: FarmingPlanTask[],
  added_in_diary: number = 0
): string[] {
  try {
    if (!dataArray.length || !originalArray.length) {
      throw new Error('Data or original array is empty');
    }

    const sqlQueries: string[] = [];

    // Get all possible field names from all tasks
    const allFieldNames = new Set<string>();
    dataArray.forEach(task => {
      Object.keys(task).forEach(key => {
        if (task[key] !== undefined && task[key] !== null && task[key] !== '') {
          allFieldNames.add(key);
        }
      });
    });

    const fieldNames = Array.from(allFieldNames);

    // Process each task individually for upsert
    dataArray.forEach((data, index) => {
      const originalTask = originalArray[index];
      const taskId = data.name || uuidv4(); // Use existing name or generate new UUID

      // Create values array with all fields
      const values = [
        `'${taskId}'`,
        added_in_diary
      ];

      // Filter out 'name' from fieldNames to avoid duplication since we already include it explicitly
      const fieldsWithoutName = fieldNames.filter(field => field !== 'name');

      // Add all fields in consistent order (excluding 'name' since it's already added)
      fieldsWithoutName.forEach(field => {
        if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
          const value = data[field];
          values.push(
            typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` :
              value === null ? 'NULL' : value
          );
        } else {
          values.push('NULL');
        }
      });

      // Create upsert query for main task
      const insertFields = ['name', 'added_in_diary', ...fieldsWithoutName].join(', ');
      const updateFields = fieldsWithoutName.map(field => `"${field}" = EXCLUDED."${field}"`).join(', ');

      const taskUpsertQuery = `
        INSERT INTO "tabiot_farming_plan_task" (${insertFields})
        VALUES (${values.join(', ')})
        ON CONFLICT (name) DO UPDATE SET
          ${updateFields},
          added_in_diary = EXCLUDED.added_in_diary
        RETURNING *;
      `;

      sqlQueries.push(taskUpsertQuery);

      // Handle related entities with upsert logic
      if (originalTask.involve_in_users && originalTask.involve_in_users.length > 0) {
        originalTask.involve_in_users.forEach((user: any) => {
          const assignUserName = `${user.customer_user}-${taskId}`;
          const assignUserQuery = `
            INSERT INTO "tabiot_assign_user" (name, customer_user, task)
            VALUES ('${assignUserName}', '${user.customer_user}', '${taskId}')
            ON CONFLICT (name) DO UPDATE SET
              customer_user = EXCLUDED.customer_user,
              task = EXCLUDED.task
            RETURNING *;
          `;
          sqlQueries.push(assignUserQuery);
        });
      }

      // Handle item_list with upsert
      if (originalTask.item_list && originalTask.item_list.length > 0) {
        originalTask.item_list.forEach((item: any) => {
          const itemName = item.name || uuidv4();
          const uomData = createSafeUOMDataForSQL(item.active_uom, item.active_conversion_factor);
          const itemQuery = `
            INSERT INTO "tabiot_warehouse_item_task_used"
            (name, quantity, description, task_id, iot_category_id, exp_quantity, loss_quantity,
             issued_quantity, total_qty_in_crop, draft_quantity, active_uom, active_conversion_factor)
            VALUES ('${itemName}', ${item.quantity || 0}, '${(item.description || '').replace(/'/g, "''")}',
                    '${taskId}', '${item.iot_category_id}', ${item.exp_quantity || 0}, ${item.loss_quantity || 0},
                    ${item.issued_quantity || 0}, ${item.total_qty_in_crop || 0}, ${item.draft_quantity || 0},
                    '${uomData.active_uom}', ${uomData.active_conversion_factor})
            ON CONFLICT (task_id, iot_category_id) DO UPDATE SET
              quantity = EXCLUDED.quantity,
              description = EXCLUDED.description,
              exp_quantity = EXCLUDED.exp_quantity,
              loss_quantity = EXCLUDED.loss_quantity,
              issued_quantity = EXCLUDED.issued_quantity,
              total_qty_in_crop = EXCLUDED.total_qty_in_crop,
              draft_quantity = EXCLUDED.draft_quantity,
              active_uom = EXCLUDED.active_uom,
              active_conversion_factor = EXCLUDED.active_conversion_factor
            RETURNING *;
          `;
          sqlQueries.push(itemQuery);
        });
      }

      // Handle prod_quantity_list with upsert
      if (originalTask.prod_quantity_list && originalTask.prod_quantity_list.length > 0) {
        console.log(`Processing ${originalTask.prod_quantity_list.length} production quantities for task ${taskId}`);
        originalTask.prod_quantity_list.forEach((qty: any, index: number) => {
          const uomData = createSafeUOMDataForSQL(qty.active_uom, qty.active_conversion_factor);
          console.log(`Production quantity ${index}: task_id=${taskId}, product_id=${qty.product_id}, name=${qty.name || 'NEW'}`);

          // For upsert, we need to handle the name field carefully
          // If qty.name exists, use it; otherwise generate a new UUID
          // But we need to ensure the ON CONFLICT works properly
          let qtyQuery;

          if (qty.name) {
            // Update existing production quantity by name
            qtyQuery = `
              INSERT INTO "tabiot_production_quantity"
              (name, quantity, unit, description, task_id, exp_quantity, label, product_id,
               lost_quantity, finished_quantity, issued_quantity, total_qty_in_crop, draft_quantity,
               active_uom, active_conversion_factor)
              VALUES ('${qty.name}', ${qty.quantity || 0}, '${qty.unit || ''}', '${(qty.description || '').replace(/'/g, "''")}',
                      '${taskId}', ${qty.exp_quantity || 0}, '${qty.label || ''}', '${qty.product_id || ''}',
                      ${qty.lost_quantity || 0}, ${qty.finished_quantity || 0}, ${qty.issued_quantity || 0},
                      ${qty.total_qty_in_crop || 0}, ${qty.draft_quantity || 0}, '${uomData.active_uom}',
                      ${uomData.active_conversion_factor})
              ON CONFLICT (name) DO UPDATE SET
                quantity = EXCLUDED.quantity,
                unit = EXCLUDED.unit,
                description = EXCLUDED.description,
                task_id = EXCLUDED.task_id,
                exp_quantity = EXCLUDED.exp_quantity,
                label = EXCLUDED.label,
                product_id = EXCLUDED.product_id,
                lost_quantity = EXCLUDED.lost_quantity,
                finished_quantity = EXCLUDED.finished_quantity,
                issued_quantity = EXCLUDED.issued_quantity,
                total_qty_in_crop = EXCLUDED.total_qty_in_crop,
                draft_quantity = EXCLUDED.draft_quantity,
                active_uom = EXCLUDED.active_uom,
                active_conversion_factor = EXCLUDED.active_conversion_factor
              RETURNING *;
            `;
          } else {
            // Create new production quantity with generated UUID
            const qtyName = uuidv4();
            qtyQuery = `
              INSERT INTO "tabiot_production_quantity"
              (name, quantity, unit, description, task_id, exp_quantity, label, product_id,
               lost_quantity, finished_quantity, issued_quantity, total_qty_in_crop, draft_quantity,
               active_uom, active_conversion_factor)
              VALUES ('${qtyName}', ${qty.quantity || 0}, '${qty.unit || ''}', '${(qty.description || '').replace(/'/g, "''")}',
                      '${taskId}', ${qty.exp_quantity || 0}, '${qty.label || ''}', '${qty.product_id || ''}',
                      ${qty.lost_quantity || 0}, ${qty.finished_quantity || 0}, ${qty.issued_quantity || 0},
                      ${qty.total_qty_in_crop || 0}, ${qty.draft_quantity || 0}, '${uomData.active_uom}',
                      ${uomData.active_conversion_factor})
              ON CONFLICT (task_id, product_id) DO UPDATE SET
                quantity = EXCLUDED.quantity,
                unit = EXCLUDED.unit,
                description = EXCLUDED.description,
                exp_quantity = EXCLUDED.exp_quantity,
                label = EXCLUDED.label,
                lost_quantity = EXCLUDED.lost_quantity,
                finished_quantity = EXCLUDED.finished_quantity,
                issued_quantity = EXCLUDED.issued_quantity,
                total_qty_in_crop = EXCLUDED.total_qty_in_crop,
                draft_quantity = EXCLUDED.draft_quantity,
                active_uom = EXCLUDED.active_uom,
                active_conversion_factor = EXCLUDED.active_conversion_factor
              RETURNING *;
            `;
          }

          sqlQueries.push(qtyQuery);
        });
      }

      // Handle todo_list with upsert
      if (originalTask.todo_list && originalTask.todo_list.length > 0) {
        originalTask.todo_list.forEach((todo: any) => {
          const todoName = todo.name || uuidv4();
          const todoQuery = `
            INSERT INTO "tabiot_todo"
            (name, label, description, status, farming_plan_task)
            VALUES ('${todoName}', '${(todo.label || '').replace(/'/g, "''")}',
                    '${(todo.description || '').replace(/'/g, "''")}', '${todo.status || 'Pending'}', '${taskId}')
            ON CONFLICT (name) DO UPDATE SET
              label = EXCLUDED.label,
              description = EXCLUDED.description,
              status = EXCLUDED.status,
              farming_plan_task = EXCLUDED.farming_plan_task
            RETURNING *;
          `;
          sqlQueries.push(todoQuery);
        });
      }

      // Handle worksheet_list with upsert
      if (originalTask.worksheet_list && originalTask.worksheet_list.length > 0) {
        originalTask.worksheet_list.forEach((worksheet: any) => {
          const worksheetName = worksheet.name || uuidv4();
          const worksheetQuery = `
            INSERT INTO "tabiot_farming_plan_task_worksheet"
            (name, label, description, quantity, type, task_id, exp_quantity, work_type_id, cost)
            VALUES ('${worksheetName}', '${(worksheet.label || '').replace(/'/g, "''")}',
                    '${(worksheet.description || '').replace(/'/g, "''")}', ${worksheet.quantity || 0},
                    '${worksheet.type || ''}', '${taskId}', ${worksheet.exp_quantity || 0},
                    '${worksheet.work_type_id || ''}', ${worksheet.cost || 0})
            ON CONFLICT (name) DO UPDATE SET
              label = EXCLUDED.label,
              description = EXCLUDED.description,
              quantity = EXCLUDED.quantity,
              type = EXCLUDED.type,
              task_id = EXCLUDED.task_id,
              exp_quantity = EXCLUDED.exp_quantity,
              work_type_id = EXCLUDED.work_type_id,
              cost = EXCLUDED.cost
            RETURNING *;
          `;
          sqlQueries.push(worksheetQuery);
        });
      }
    });

    return sqlQueries;
  } catch (error) {
    throw error;
  }
}

export function generateInsertSQLTaskArray(
  dataArray: FarmingPlanTask[],
  originalArray: FarmingPlanTask[],
  added_in_diary: number = 0
): string[] {
  try {
    if (!dataArray.length || !originalArray.length) {
      throw new Error('Data or original array is empty');
    }

    // Initialize SQL queries
    let sqlQueryCreateTask = `INSERT INTO "tabiot_farming_plan_task" (`;
    let sqlQueryCreateAssignUser = `INSERT INTO "tabiot_assign_user" (name, customer_user, task) VALUES`;

    // Get all possible field names from all tasks
    const allFieldNames = new Set<string>();
    dataArray.forEach(task => {
      Object.keys(task).forEach(key => {
        if (task[key] !== undefined && task[key] !== null && task[key] !== '') {
          allFieldNames.add(key);
        }
      });
    });

    const fieldNames = Array.from(allFieldNames).join(', ');
    sqlQueryCreateTask += `name, added_in_diary, ${fieldNames}) VALUES `;

    const d = originalArray[0];

    // Initialize nested SQL queries only if lists are non-empty
    const todoFieldNames = d.todo_list?.length
      ? Object.keys(d.todo_list[0]).filter(k => k !== 'name' && d.todo_list![0][k] !== '' && d.todo_list![0][k] !== undefined).join(', ')
      : '';
    let sqlQueryCreateTodo = todoFieldNames
      ? `INSERT INTO "tabiot_todo" (name, ${todoFieldNames}, farming_plan_task) VALUES `
      : '';

    const itemFieldNames = d.item_list?.length
      ? Object.keys(d.item_list[0]).filter(k => k !== 'name' && d.item_list![0][k] !== '' && d.item_list![0][k] !== undefined).join(', ')
      : '';
    let sqlQueryCreateiotItem = itemFieldNames
      ? `INSERT INTO "tabiot_warehouse_item_task_used" (name, ${itemFieldNames}, task_id) VALUES `
      : '';

    const workSheetFieldNames = d.worksheet_list?.length
      ? Object.keys(d.worksheet_list[0]).filter(k => k !== 'name' && d.worksheet_list![0][k] !== '' && d.worksheet_list![0][k] !== undefined).join(', ')
      : '';
    let sqlQueryCreateiotWorksheet = workSheetFieldNames
      ? `INSERT INTO "tabiot_farming_plan_task_worksheet" (name, ${workSheetFieldNames}, task_id) VALUES `
      : '';

    const prodQuantityFieldNames = d.prod_quantity_list?.length
      ? Object.keys(d.prod_quantity_list[0]).filter(k => k !== 'name' && d.prod_quantity_list![0][k] !== '' && d.prod_quantity_list![0][k] !== undefined).join(', ')
      : '';
    let sqlQueryCreateProdQuantity = prodQuantityFieldNames
      ? `INSERT INTO "tabiot_production_quantity" (name, ${prodQuantityFieldNames}, task_id) VALUES `
      : '';

    let checkAssignUser = false;

    dataArray.forEach((data, index) => {
      const randomUUID = uuidv4();

      // Create values array with all fields, using default values for missing fields
      const values = [
        `'${randomUUID}'`,
        added_in_diary
      ];

      // Add all fields in consistent order
      Array.from(allFieldNames).forEach(field => {
        if (data[field] !== undefined && data[field] !== null && data[field] !== '') {
          const value = data[field];
          values.push(
            typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` :
              value === null ? 'NULL' : value
          );
        } else {
          // Add default value for missing field
          values.push('NULL');
        }
      });

      sqlQueryCreateTask += `(${values.join(', ')}),`;

      originalArray.forEach((d, i) => {
        if (index === i) {
          // Handle involve_in_users
          if (d.involve_in_users?.length) {
            d.involve_in_users.forEach((involve) => {
              const assignUserUUID = `${involve.customer_user}-${randomUUID}`;
              sqlQueryCreateAssignUser += `('${assignUserUUID}', ${escapeSQLValue(involve.customer_user)}, '${randomUUID}'),`;
              checkAssignUser = true;
            });
          }

          // Handle todo_list
          if (todoFieldNames && d.todo_list?.length) {
            d.todo_list.forEach((todo) => {
              const todoUUID = uuidv4();
              const { name: _, ...restTodo } = todo;
              const validTodo = Object.fromEntries(
                Object.entries(restTodo).filter(([_, value]) => value !== '' && value !== undefined)
              );
              const dataValues = getObjectValues(validTodo).join(', ');
              sqlQueryCreateTodo += `('${todoUUID}', ${dataValues}, '${randomUUID}'),`;
            });
          }

          // Handle item_list
          if (itemFieldNames && d.item_list?.length) {
            d.item_list.forEach((item) => {
              const itemUUID = uuidv4();
              const { name: _, ...restItem } = item;
              const validItem = Object.fromEntries(
                Object.entries(restItem).filter(([_, value]) => value !== '' && value !== undefined)
              );
              const dataValues = getObjectValues(validItem).join(', ');
              sqlQueryCreateiotItem += `('${itemUUID}', ${dataValues}, '${randomUUID}'),`;
            });
          }

          // Handle worksheet_list
          if (workSheetFieldNames && d.worksheet_list?.length) {
            d.worksheet_list.forEach((worksheet) => {
              const worksheetUUID = uuidv4();
              const { name: _, ...restWs } = worksheet;
              const validWs = Object.fromEntries(
                Object.entries(restWs).filter(([_, value]) => value !== '' && value !== undefined)
              );
              const dataValues = getObjectValues(validWs).join(', ');
              sqlQueryCreateiotWorksheet += `('${worksheetUUID}', ${dataValues}, '${randomUUID}'),`;
            });
          }

          // Handle prod_quantity_list
          if (prodQuantityFieldNames && d.prod_quantity_list?.length) {
            d.prod_quantity_list.forEach((prodQuantity) => {
              const prodQuantityUUID = uuidv4();
              const { name: _, ...restPQ } = prodQuantity;
              const validPQ = Object.fromEntries(
                Object.entries(restPQ).filter(([_, value]) => value !== '' && value !== undefined)
              );
              const dataValues = getObjectValues(validPQ).join(', ');
              sqlQueryCreateProdQuantity += `('${prodQuantityUUID}', ${dataValues}, '${randomUUID}'),`;
            });
          }
        }
      });
    });

    // Finalize queries
    const returnArr: string[] = [];

    sqlQueryCreateTask = sqlQueryCreateTask.slice(0, -1) + ` RETURNING *`;
    returnArr.push(sqlQueryCreateTask);

    if (checkAssignUser) {
      sqlQueryCreateAssignUser = sqlQueryCreateAssignUser.slice(0, -1) + ` RETURNING *`;
      returnArr.push(sqlQueryCreateAssignUser);
    }

    if (todoFieldNames && sqlQueryCreateTodo) {
      sqlQueryCreateTodo = sqlQueryCreateTodo.slice(0, -1) + ` RETURNING *`;
      returnArr.push(sqlQueryCreateTodo);
    }

    if (itemFieldNames && sqlQueryCreateiotItem) {
      sqlQueryCreateiotItem = sqlQueryCreateiotItem.slice(0, -1) + ` RETURNING *`;
      returnArr.push(sqlQueryCreateiotItem);
    }

    if (workSheetFieldNames && sqlQueryCreateiotWorksheet) {
      sqlQueryCreateiotWorksheet = sqlQueryCreateiotWorksheet.slice(0, -1) + ` RETURNING *`;
      returnArr.push(sqlQueryCreateiotWorksheet);
    }

    if (prodQuantityFieldNames && sqlQueryCreateProdQuantity) {
      sqlQueryCreateProdQuantity = sqlQueryCreateProdQuantity.slice(0, -1) + ` RETURNING *`;
      returnArr.push(sqlQueryCreateProdQuantity);
    }
    console.log("returnArr", returnArr)
    return returnArr;
  } catch (error) {
    throw error instanceof Error ? error : new Error('An unexpected error occurred');
  }
}

////////////////////////////FOR UPDATE task array///////////////////////////////

export function generateUpdateSQLTaskArray(dataArray: any[], originalArray: any[]) {
  try {
    if (dataArray.length === 0) {
      throw new Error('Data array is empty');
    }

    const involveInArray: any[] = [];
    const iot_farming_plan_task_worksheetArray: any[] = [];
    const iot_warehouse_item_task_usedArray: any[] = [];
    const todo_listArray: any[] = [];
    const prod_quantity_listArray: any[] = [];

    originalArray.forEach((data: any) => {
      data.involve_in_users.forEach((invole_in: any) => {
        invole_in.task = data.name;
        involveInArray.push(invole_in);
      });

      data.worksheet_list.forEach((worksheet: any) => {
        worksheet.task_id = data.name;
        iot_farming_plan_task_worksheetArray.push(worksheet);
      });

      data.item_list.forEach((item: any) => {
        item.task_id = data.name;
        iot_warehouse_item_task_usedArray.push(item);
      });

      data.todo_list.forEach((todo: any) => {
        todo.farming_plan_task = data.name;
        todo_listArray.push(todo);
      });

      data.prod_quantity_list.forEach((prod_quantity: any) => {
        prod_quantity.task_id = data.name;
        prod_quantity_listArray.push(prod_quantity);
      });
    });

    const queries = [
      generateUpdateOrInsertQueries('tabiot_farming_plan_task', dataArray),
      generateUpdateOrInsertQueries('tabiot_assign_user', involveInArray),
      generateUpdateOrInsertQueries('tabiot_farming_plan_task_worksheet', iot_farming_plan_task_worksheetArray),
      generateUpdateOrInsertQueries('tabiot_warehouse_item_task_used', iot_warehouse_item_task_usedArray),
      generateUpdateOrInsertQueries('tabiot_todo', todo_listArray),
      generateUpdateOrInsertQueries('tabiot_production_quantity', prod_quantity_listArray),
    ];

    // console.log(queries);
    return queries;
  } catch (error) {
    throw error;
  }
}

export function generateUpdateOrInsertQueries(tableName: string, dataArray: any[]) {
  const queries: any[] = [];

  dataArray.forEach(item => {
    if ('name' in item) {
      const updateFields = Object.keys(item).filter(field => field !== 'name'); // Exclude 'name' field from update

      let paramCounter = 1;
      const setStatements = updateFields.map(field => `"${field}" = $${paramCounter++}`).join(', ');
      const queryParamsForItem = updateFields.map(field => item[field]);

      queries.push({
        query: `UPDATE "${tableName}" SET ${setStatements} WHERE "name" = $${paramCounter}`,
        params: [...queryParamsForItem, item.name],
      });
    } else {
      const insertFields = Object.keys(item);
      const paramPlaceholders = insertFields.map((_, index) => `$${index + 1}`).join(', ');
      const queryParamsForItem = insertFields.map(field => item[field]);
      const randomNameUUID = uuidv4();
      if (tableName === `tabiot_assign_user`) {
        const name = `${item.customer_user}-${item.task}`;
        queries.push({
          query: `INSERT INTO "${tableName}" (name, ${insertFields
            .map(field => `"${field}"`)
            .join(', ')}) VALUES('${name}', ${paramPlaceholders})`,
          params: queryParamsForItem,
        });
      } else {
        queries.push({
          query: `INSERT INTO "${tableName}" (name, ${insertFields
            .map(field => `"${field}"`)
            .join(', ')}) VALUES('${randomNameUUID}', ${paramPlaceholders})`,
          params: queryParamsForItem,
        });
      }
    }
  });
  return queries;
}

function generateDeleteQueries(tableName: string, dataArray: any[]): { query: string; params: [] }[] {
  const queries: any[] = [];

  dataArray.forEach((data: any) => {
    if (data.is_deleted) {
      delete data.is_deleted; // Xóa trường is_deleted khỏi đối tượng
      const params: string[] = [];
      const conditions: string[] = [];

      Object.keys(data).forEach((key: string) => {
        if (key !== 'is_deleted') {
          conditions.push(`${key} = $${params.length + 1}`);
          params.push(data[key]);
        }
      });

      const query = `DELETE FROM ${tableName} WHERE ${conditions.join(' AND ')}`;
      queries.push({ query, params });
    }
  });

  return queries;
}

export function generateDeleteSQLTaskArray(dataArray: any[]) {
  const deleteQueries = [
    generateDeleteQueries(
      'tabiot_assign_user',
      dataArray.flatMap(data => data.involve_in_users),
    ),
    generateDeleteQueries(
      'tabiot_farming_plan_task_worksheet',
      dataArray.flatMap(data => data.worksheet_list),
    ),
    generateDeleteQueries(
      'tabiot_warehouse_item_task_used',
      dataArray.flatMap(data => data.item_list),
    ),
    generateDeleteQueries(
      'tabiot_todo',
      dataArray.flatMap(data => data.todo_list),
    ),
    generateDeleteQueries(
      'tabiot_production_quantity',
      dataArray.flatMap(data => data.prod_quantity_list),
    ),
  ];

  return deleteQueries;
}

function generateUpdateQueries(tableName: any, dataArray: any) {
  const updateQueries: any = [];

  dataArray.forEach((item: any) => {
    const updateFields = Object.keys(item).filter(field => field !== 'name'); // Exclude 'name' field from update

    let paramCounter = 1;
    const setStatements = updateFields.map(field => `"${field}" = $${paramCounter++}`).join(', ');
    const queryParamsForItem = updateFields.map(field => item[field]);

    updateQueries.push({
      query: `UPDATE "${tableName}" SET ${setStatements} WHERE "name" = $${paramCounter}`,
      params: [...queryParamsForItem, item.name],
    });
  });

  return updateQueries;
}

////////////////FOR STORAGE MANAGEMENT//////////////
export function generateRandomNumber(length: number): string {
  const randomNumber = Math.floor(Math.random() * Math.pow(10, length));
  return randomNumber.toString().padStart(length, '0');
}

export function getCurrentDateFormatted(): string {
  const now = new Date();
  const year = now.getFullYear().toString().substr(2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  return year + month + day;
}

export async function verifyStorageAccess(user: ICurrentUser, storageName: string) {
  const storageList = await getStorageList(user);
  const hasAccess = storageList.some(obj => obj.name === storageName);

  if (!hasAccess) {
    throw new HttpError(403, "You don't have permission to create this iot_import/iot_export transaction");
  }
}

export async function getStorageList(user: ICurrentUser) {
  const storageList = await ERPExecute(
    `
              SELECT 
                  storage.name, storage.zone_id, storage.customer_id
              FROM "tabiot_storage" as storage
              WHERE
              storage.customer_id = $1
          `,
    [user.customer_id],
  );
  return storageList;
}

export function generateInventoryCheckingName(type?: 'category' | 'product') {
  console.log('generateInventoryCheckingName');
  if (type === 'category') {
    return `ICC-${getCurrentDateFormatted()}-${generateRandomNumber(5)}`;
  } else if (type === 'product') {
    return `ICD-${getCurrentDateFormatted()}-${generateRandomNumber(5)}`;
  } else return '';
}

export function generateImportName() {
  console.log('generateImportName');
  return `IP-${getCurrentDateFormatted()}-${generateRandomNumber(5)}`;
}

export function generateExportName() {
  return `EP-${getCurrentDateFormatted()}-${generateRandomNumber(5)}`;
}

export function generateIotImportSQL(importName: string, body: IIotImportTransaction, user: ICurrentUser): SqlQuery {
  console.log('generateIotImportSQL');
  return {
    query: `
            INSERT INTO tabiot_import (
                name, naming_series, storage, transaction_date, document_date,
                document_code, manufacturing_date, expiry_date, region, employee,
                description, supplier, supplier_phone_number, note, creation, modified
            )
            VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()
            )
            RETURNING *
        `,
    params: [
      importName,
      'IP-.DD.MM.YY.-',
      body.storage,
      body.transaction_date,
      body.document_date,
      body.document_code,
      body.manufacturing_date,
      body.expiry_date,
      body.region,
      body.employee,
      body.description,
      body.supplier,
      body.supplier_phone_number,
      body.note,
    ],
  };
}

export function generateIotExportSQL(exportName: string, body: IIotExportTransaction, user: ICurrentUser): SqlQuery {
  return {
    query: `
            INSERT INTO tabiot_export (
                name, naming_series, storage, transaction_date, document_date,
                document_code, manufacturing_date, expiry_date, region, employee,
                description, supplier, supplier_phone_number, note, export_type, creation, modified
            )
            VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW()
            )
            RETURNING *
        `,
    params: [
      exportName,
      'EP-.DD.MM.YY.-',
      body.storage,
      body.transaction_date,
      body.document_date,
      body.document_code,
      body.manufacturing_date,
      body.expiry_date,
      body.region,
      body.employee,
      body.description,
      body.supplier,
      body.supplier_phone_number,
      body.note,
      body.export_type,
    ],
  };
}

export function generateIotImportDetailSQLs(importName: string, importDetails: IIotImportDetail[]): SqlQuery[] {
  const iotImportDetailSQLArr: SqlQuery[] = [];
  console.log('IIotImportDetail', importDetails);
  for (const element of importDetails) {
    const importDetailName = uuidv4();
    let params: any[] = [importDetailName, importName, element.type];

    if (element.type === 'product') {
      params.push(null); // category_entity
      params.push(element.product_entity);
    } else if (element.type === 'category') {
      params.push(element.category_entity);
      params.push(null); // product_entity
    } else {
      throw new Error('Incorrect iot_import_detail type');
    }
    // const unit_price = element.unit_price !== undefined && element.unit_price !== null ? element.unit_price : 0;
    //check element.quantity_type, if "packing" => unit_price = element.unit_price divide element.conversion_factor
    // else unit_price = element.unit_price
    let unit_price = element.unit_price;
    // if (element.quantity_type === 'packing') {
    //   unit_price = element.unit_price / element.conversion_factor;
    // } else {
    //   unit_price = element.unit_price;
    // }
    const total_price = element.total_price !== undefined && element.total_price !== null ? element.total_price : 0;
    const discount = element.discount !== undefined && element.discount !== null ? element.discount : 0;
    const supplier = element.supplier !== undefined && element.supplier !== null ? element.supplier : '';

    params.push(discount, element.quantity, total_price, supplier, unit_price);
    if (params.length !== 10) {
      throw new Error('Incorrect number of parameters in the SQL query');
    }
    const iotImportDetailSQL: SqlQuery = {
      query: `
                INSERT INTO tabiot_import_detail (
                    name, import_history, type, category_entity, product_entity,
                    discount, quantity, total_price, supplier, unit_price
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING *;
            `,
      params,
    };
    iotImportDetailSQLArr.push(iotImportDetailSQL);
  }

  // console.log('iotImportDetailSQLArr:', iotImportDetailSQLArr);
  return iotImportDetailSQLArr;
}

export function generateIotExportDetailSQLs(
  exportName: string,
  exportDetails: (IIotImportDetail | IIotExportDetail)[],
): SqlQuery[] {
  const iotExportDetailSQLArr: SqlQuery[] = [];

  for (const element of exportDetails) {
    const exportDetailName = uuidv4();
    let params: any[] = [exportDetailName, exportName, element.type];

    if (element.type === 'product') {
      params.push(null); // category_entity
      params.push(element.product_entity);
    } else if (element.type === 'category') {
      params.push(element.category_entity);
      params.push(null); // product_entity
    } else {
      throw new Error('Incorrect iot_export_detail type');
    }
    // const unit_price = element.unit_price !== undefined && element.unit_price !== null ? element.unit_price : 0;
    //similar to import
    let unit_price = element.unit_price;
    // if (element.quantity_type === 'packing') {
    //   unit_price = element.unit_price / element.conversion_factor;
    // } else {
    //   unit_price = element.unit_price;
    // }
    const total_price = element.total_price !== undefined && element.total_price !== null ? element.total_price : 0;
    const discount = element.discount !== undefined && element.discount !== null ? element.discount : 0;
    const supplier = element.supplier !== undefined && element.supplier !== null ? element.supplier : '';

    params.push(discount, element.quantity, total_price, supplier, unit_price);
    if (params.length !== 10) {
      throw new Error('Incorrect number of parameters in the SQL query');
    }
    const iotExportDetailSQL: SqlQuery = {
      query: `
                INSERT INTO tabiot_export_detail (
                    name, export_history, type, category_entity, product_entity,
                    discount, quantity, total_price, supplier, unit_price
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING *;
            `,
      params,
    };
    iotExportDetailSQLArr.push(iotExportDetailSQL);
  }

  return iotExportDetailSQLArr;
}

/**
 * Generates SQL queries for updating the IoT inventory based on the provided details.
 * @param details An array of IIotImportDetail or IIotExportDetail objects containing import/export details.
 * @param storage The storage identifier.
 * @param transactionType The type of transaction (import/export).
 * @returns An array of SqlQuery objects representing the generated SQL queries.
 */
export async function generateIotInventorySQLs(
  details: (IIotImportDetail | IIotExportDetail)[],
  storage: string,
  transactionType: string,
): Promise<SqlQuery[]> {
  // Initialize an object to store cumulative quantities for each category_entity or product_entity
  const quantities: { [key: string]: number } = {};

  // Determine the type of detail records (category or product)
  const typeCheck: 'category' | 'product' = details[0].type;

  // Calculate cumulative quantities based on transaction type and detail records
  for (const element of details) {
    const key = typeCheck === 'category' ? element.category_entity ?? 'default' : element.product_entity ?? 'default';
    console.log('type of quantity', typeof element.quantity);

    // Update cumulative quantities based on transaction type
    quantities[key] = (quantities[key] || 0) + (transactionType === 'import' ? element.quantity : -element.quantity);
  }

  // Initialize an array to store the generated SQL queries
  const iotInventorySQLArr: SqlQuery[] = [];

  // Generate SQL queries for each entity and cumulative quantity
  for (const [entity, cumulativeQuantity] of Object.entries(quantities)) {
    // Determine the appropriate table and entity column based on the type of detail records
    const tableName = typeCheck === 'category' ? 'tabiot_category_inventory' : 'tabiot_product_inventory';
    const entityColumn = typeCheck === 'category' ? 'category_entity' : 'product_entity';

    // Check and handle export scenario to ensure available quantity
    if (transactionType === 'export') {
      const quantityInDB = await getCurrentQuantityFromDatabase(entity, typeCheck, storage);
      if (quantityInDB + cumulativeQuantity < 0) {
        throw new HttpError(500, 'Số lượng xuất kho vượt quá số lượng có sẵn trong kho');
      }
    }

    // Create a unique name for the entity and storage combination
    const name = `${entity}-${storage}`;

    //inventory price of entity
    const entityPrice = await getEntityPrice(entity, typeCheck);
    console.log('entity price is', entityPrice);
    const inventoryPrice = entityPrice ? entityPrice * cumulativeQuantity : 0;
    console.log('entityPrice', entityPrice, 'inventoryPrice', inventoryPrice);
    // Define the SQL query for inserting or updating the inventory
    const query = `
            INSERT INTO ${tableName} (name, ${entityColumn}, storage, quantity, price)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (${entityColumn}, storage) DO UPDATE
            SET quantity = ${tableName}.quantity + excluded.quantity,
            price = ${tableName}.price + excluded.price;
        `;

    // Define the parameters for the SQL query
    const params = [name, entity, storage, cumulativeQuantity, inventoryPrice];

    // Add the SQL query and parameters to the array
    iotInventorySQLArr.push({ query, params });
  }

  // Return the array of generated SQL queries
  return iotInventorySQLArr;
}

/**
 * Generates SQL queries for updating the IoT inventory based on the provided details.
 * @param details An array of IIotImportDetail or IIotExportDetail objects containing import/export details.
 * @param storage The storage identifier.
 * @param transactionType The type of transaction (import/export).
 * @returns An array of SqlQuery objects representing the generated SQL queries.
 */
export async function generateUpdateIotInventorySQLs(
  oldDetails: (IIotImportDetail | IIotExportDetail)[],
  details: (IIotImportDetail | IIotExportDetail)[],
  storage: string,
  transactionType: string,
): Promise<SqlQuery[]> {
  // Initialize an object to store cumulative quantities for each category_entity or product_entity
  const quantities: { [key: string]: number } = {};

  // Determine the type of detail records (category or product)
  const typeCheck: 'category' | 'product' = details[0].type;

  // Calculate cumulative quantities based on transaction type for oldDetails
  for (const element of oldDetails) {
    const key = typeCheck === 'category' ? element.category_entity ?? 'default' : element.product_entity ?? 'default';
    const quantity = transactionType === 'import' ? -element.quantity : +element.quantity;

    quantities[key] = (quantities[key] || 0) + quantity;
  }

  // Calculate cumulative quantities based on transaction type for details
  for (const element of details) {
    const key = typeCheck === 'category' ? element.category_entity ?? 'default' : element.product_entity ?? 'default';
    const quantity = transactionType === 'import' ? +element.quantity : -element.quantity;
    quantities[key] = (quantities[key] || 0) + quantity;
  }

  // Initialize an array to store the generated SQL queries
  const iotInventorySQLArr: SqlQuery[] = [];

  // Generate SQL queries for each entity and cumulative quantity
  for (const [entity, cumulativeQuantity] of Object.entries(quantities)) {
    // Determine the appropriate table and entity column based on the type of detail records
    const tableName = typeCheck === 'category' ? 'tabiot_category_inventory' : 'tabiot_product_inventory';
    const entityColumn = typeCheck === 'category' ? 'category_entity' : 'product_entity';

    // Check and handle export scenario to ensure available quantity
    if (transactionType === 'export') {
      const quantityInDB = await getCurrentQuantityFromDatabase(entity, typeCheck, storage);
      if (quantityInDB + cumulativeQuantity < 0) {
        throw new HttpError(500, 'Số lượng xuất kho vượt quá số lượng có sẵn trong kho');
      }
    }

    // Create a unique name for the entity and storage combination
    const name = `${entity}-${storage}`;

    //inventory price of entity
    const entityPrice = await getEntityPrice(entity, typeCheck);
    console.log('entity price is', entityPrice, 'cumulativeQuantity', cumulativeQuantity);
    const inventoryPrice = entityPrice ? entityPrice * cumulativeQuantity : 0;
    console.log('entityPrice', entityPrice, 'cumulativeQuantity', cumulativeQuantity, 'inventoryPrice', inventoryPrice);
    // Define the SQL query for inserting or updating the inventory
    const query = `
            INSERT INTO ${tableName} (name, ${entityColumn}, storage, quantity, price)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (${entityColumn}, storage) DO UPDATE
            SET quantity = ${tableName}.quantity + excluded.quantity,
            price = ${tableName}.price + excluded.price;
        `;

    // Define the parameters for the SQL query
    const params = [name, entity, storage, cumulativeQuantity, inventoryPrice];

    // Add the SQL query and parameters to the array
    iotInventorySQLArr.push({ query, params });
  }

  // Return the array of generated SQL queries
  return iotInventorySQLArr;
}

export async function getCurrentQuantityFromDatabase(
  entity: string,
  typeCheck: 'category' | 'product',
  storage: string,
): Promise<number> {
  // Implement database query to retrieve current quantity
  let getQuantitySQL = '';
  switch (typeCheck) {
    case 'category':
      getQuantitySQL = `
            SELECT quantity
            FROM tabiot_category_inventory 
            WHERE category_entity = $1 AND storage = $2;
            `;
      break;
    case 'product':
      getQuantitySQL = `
            SELECT quantity, name, product_entity
            FROM tabiot_product_inventory 
            WHERE product_entity = $1 AND storage = $2;
            `;
      break;
    default:
      break;
  }
  const getQuantity = await ERPExecute(getQuantitySQL, [entity, storage]);
  if (!getQuantity.length) {
    throw new HttpError(500, 'Kho này không có tồn kho của vật tư hay sản phẩm đã chỉ định, không thể xuất kho');
  }
  console.log('getQuantity', getQuantity);
  return parseFloat(getQuantity[0].quantity);
}

export async function getEntityPrice(entity: string, typeCheck: 'category' | 'product'): Promise<number> {
  // Implement database query to retrieve current quantity
  let getPriceSQL = '';
  switch (typeCheck) {
    case 'category':
      getPriceSQL = `
            SELECT category_price
            FROM tabiot_category 
            WHERE name = $1;
            `;
      break;
    case 'product':
      getPriceSQL = `
            SELECT product_price
            FROM tabiot_agriculture_product 
            WHERE name = $1;
            `;
      break;
    default:
      break;
  }
  const getQuantity = await ERPExecute(getPriceSQL, [entity]);
  // console.log('getQuantity', getQuantity);
  if (typeCheck === 'category') {
    return parseFloat(getQuantity[0].category_price);
  } else {
    return parseFloat(getQuantity[0].product_price);
  }
}

export function checkImportOrExportDetailTypes(details: (IIotImportDetail | IIotExportDetail)[]): boolean {
  let areAllTypesEqual = true;
  let commonType: string | undefined = undefined;

  for (const element of details) {
    if (!commonType) {
      commonType = element.type;
    } else {
      if (element.type !== commonType) {
        areAllTypesEqual = false;
        break;
      }
    }
  }

  return areAllTypesEqual;
}

/**
 * This function parses filter parameters and returns a SQL condition string.
 * The input is an array of filter parameters, each of which is an array itself.
 * Each filter parameter array can have either 3 or 4 elements.
 * If it has 4 elements, they represent ["doctype_name","field_name","operator","value"].
 * If it has 3 elements, they represent ["field_name","operator","value"] and doctype_name is assumed to be null.
 * The function returns a string SQL condition starting with AND, concatenated with AND for each condition.
 * If the operator is "like", the value will be wrapped in percentage signs.
 *
 * @param filterParams - An array of filter parameters.
 * @returns A string representing a SQL condition.
 * @throws Will throw an error if a filter parameter array does not have 3 or 4 elements.
 */
export function parseFilterParams(filterParams: any[]): string {
  let sqlCondition = '';

  filterParams.forEach((filter: any) => {
    let doctypeName: string | null = null;
    let fieldName: string;
    let operator: string;
    let value: any;

    // Kiểm tra độ dài của mảng filter và gán giá trị tương ứng
    if (filter.length === 4) {
      [doctypeName, fieldName, operator, value] = filter;
    } else if (filter.length === 3) {
      [fieldName, operator, value] = filter;
    } else {
      throw new Error('Định dạng bộ lọc không hợp lệ');
    }

    // Nếu là toán tử IN, xử lý danh sách giá trị
    if (operator.toUpperCase() === 'IN' && Array.isArray(value)) {
      // Chuyển mảng giá trị thành một chuỗi SQL IN ('value1', 'value2', ...)
      let formattedValues = value.map((v: any) => `'${v}'`).join(', ');
      value = `(${formattedValues})`;
    } else if (operator === 'like') {
      // Format giá trị nếu toán tử là "like"
      value = `'%${value}%'`;
    } else {
      // Các trường hợp còn lại, format giá trị bình thường
      value = `'${value}'`;
    }

    // Thêm prefix của bảng nếu có doctypeName
    let tablePrefix = doctypeName ? `"tab${doctypeName}".` : '';

    // Thêm điều kiện vào chuỗi SQL
    if (operator === 'like') {
      sqlCondition += ` AND unaccent(${tablePrefix}"${fieldName}") ILIKE unaccent(${value})`;
    } else {
      sqlCondition += ` AND ${tablePrefix}"${fieldName}" ${operator} ${value}`;
    }
  });

  return sqlCondition;
}

// Helper function to get default values
export function getDefault(value: any, defaultValue: any) {
  return value ? value : defaultValue;
}

// Helper function to paginate an array
export function paginate(array: any[], page: number, pageSize: number) {
  const start = (page - 1) * pageSize;
  const end = page * pageSize;
  return array.slice(start, end);
}

//get counter value from database for auto increment naming
export async function getNextCounterNumber(params: { counter_id: string }) {
  const result = await ERPExecute(
    `
    UPDATE "tabiot_counter"
    SET value = value + 1
    WHERE name = $1
    RETURNING value
  `,
    [params.counter_id],
  );

  // Return the counter padded with zeros to be 5 digits long
  return String(result[0].value).padStart(5, '0');
}

export function standardizeDateFormatToYYYY_MM_DD(date: string): string {
  const formats = [
    'YYYY-MM-DD',
    'DD-MM-YYYY',
    'MM-DD-YYYY',
    'YYYY/MM/DD',
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD HH:mm:ss',
    'DD-MM-YYYY HH:mm:ss',
    'DD-MM-YYYY HH:mm',
    'MM-DD-YYYY HH:mm:ss',
    'YYYY/MM/DD HH:mm:ss',
    'DD/MM/YYYY HH:mm:ss',
    'MM/DD/YYYY HH:mm:ss',
    'YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]', // new format
    'YYYY-MM-DDTHH:mm:ss.SSSSSS', // new format without [Z]
  ];
  for (let format of formats) {
    if (moment(date, format, true).isValid()) {
      return moment(date, format).format('YYYY-MM-DD');
    }
  }
  throw new Error(`Invalid date format: ${date}`);
}

export function standardizeDateFormatToDD_MM_YYYY(date: string): string {
  const formats = [
    'YYYY-MM-DD',
    'DD-MM-YYYY',
    'MM-DD-YYYY',
    'YYYY/MM/DD',
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD HH:mm:ss',
    'DD-MM-YYYY HH:mm:ss',
    'DD-MM-YYYY HH:mm',
    'MM-DD-YYYY HH:mm:ss',
    'YYYY/MM/DD HH:mm:ss',
    'DD/MM/YYYY HH:mm:ss',
    'MM/DD/YYYY HH:mm:ss',
    'YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]', // new format
    'YYYY-MM-DDTHH:mm:ss.SSSSSS', // new format without [Z]
  ];
  for (let format of formats) {
    if (moment(date, format, true).isValid()) {
      return moment(date, format).format('DD-MM-YYYY');
    }
  }
  throw new Error(`Invalid date format: ${date}`);
}

export function standardizeDateFormatToYYYY_MM_DD_HH_mm_ss(date: string): string {
  const formats = [
    'YYYY-MM-DD',
    'DD-MM-YYYY',
    'MM-DD-YYYY',
    'YYYY/MM/DD',
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD HH:mm:ss',
    'DD-MM-YYYY HH:mm:ss',
    'DD-MM-YYYY HH:mm',
    'MM-DD-YYYY HH:mm:ss',
    'YYYY/MM/DD HH:mm:ss',
    'DD/MM/YYYY HH:mm:ss',
    'MM/DD/YYYY HH:mm:ss',
    'YYYY-MM-DDTHH:mm:ss.SSSSSS[Z]', // new format
    'YYYY-MM-DDTHH:mm:ss.SSSSSS', // new format without [Z]
  ];
  for (let format of formats) {
    if (moment(date, format, true).isValid()) {
      return moment(date, format).format('YYYY-MM-DD HH:mm:ss');
    }
  }
  throw new Error(`Invalid date format: ${date}`);
}

export async function getCropFromTaskName(taskName: string) {
  const query = `
  SELECT
    crop.name,
    crop.label
  FROM tabiot_crop AS crop
  INNER JOIN tabiot_farming_plan AS farming_plan
  ON crop.name = farming_plan.crop
  INNER JOIN tabiot_farming_plan_state AS farming_plan_state
  ON farming_plan.name = farming_plan_state.farming_plan
  INNER JOIN tabiot_farming_plan_task AS farming_plan_task
  ON farming_plan_state.name = farming_plan_task.farming_plan_state
  WHERE farming_plan_task.name = $1
  `;
  const result = await ERPExecute(query, [taskName]);
  return result[0].name;
}

export async function getDeviceByDeviceId(deviceId: string): Promise<{ name: string }[]> {
  try {
    const query = `
      SELECT
        device.name
      FROM tabiot_device AS device
      WHERE device.name = $1
      `;
    const result = await ERPExecute(query, [deviceId]);
    return result;
  } catch (error) {
    throw error
  }

}