import { CleanUpdateDataOptions } from '../types/TaskManagementTypes';

/**
 * Utility functions for cleaning and preparing task data
 */
export class TaskCleanupUtils {
  /**
   * Clean update data object by removing relation fields and computed properties
   * that can't be directly updated through TypeORM's QueryBuilder
   */
  static cleanUpdateDataObject(updateData: any, options: CleanUpdateDataOptions = {}): void {
    if (!updateData) return;

    const {
      excludeRelations = true,
      excludeComputedProperties = true,
      excludeItemLists = true
    } = options;

    if (excludeRelations) {
      // Remove relation fields that can't be updated via QueryBuilder
      delete updateData.nextTasks;
      delete updateData.previousTask;
      delete updateData.farmingPlanState;
      delete updateData.assignedUser;
      delete updateData.department;
      delete updateData.environmentTemplate;
      delete updateData.template;
      delete updateData.vietplantsExtension;

      // Remove the newly added transfer relations
      delete updateData.incomingTransfers;
      delete updateData.outgoingTransfers;
    }

    if (excludeComputedProperties) {
      // Remove computed properties added by our API
      delete updateData.taskChainInfo;
      delete updateData.taskTransferInfo;
      delete updateData.productionInfo;
      delete updateData.warehouseItemsInfo;
      delete updateData.debugInfo;
    }

    if (excludeItemLists) {
      // Remove item lists that are handled separately
      delete updateData.item_list;
      delete updateData.prod_quantity_list;
      delete updateData.task_transfers;
    }
  }

  /**
   * Prepare task data for creation with default values
   */
  static prepareTaskDataForCreation(taskData: any, userId: string, customerId?: string): any {
    const preparedData = { ...taskData };

    // Set audit fields
    preparedData.created = new Date();
    preparedData.created_by = userId;
    preparedData.modified = new Date();
    preparedData.modified_by = userId;

    // Set ownership if available
    if (customerId) {
      preparedData.owner = customerId.toString();
    }

    return preparedData;
  }

  /**
   * Prepare warehouse item data for creation
   */
  static prepareWarehouseItemForCreation(itemData: any, taskId: string, userId: string, customerId?: string): any {
    const preparedItem = { ...itemData };

    // Set required fields
    preparedItem.task_id = taskId;

    // Set default values for other fields
    preparedItem.exp_quantity = preparedItem.exp_quantity || 0;
    preparedItem.loss_quantity = preparedItem.loss_quantity || 0;
    preparedItem.issued_quantity = preparedItem.issued_quantity || 0;
    preparedItem.total_qty_in_crop = preparedItem.total_qty_in_crop || 0;
    preparedItem.draft_quantity = preparedItem.draft_quantity || 0;
    preparedItem.active_conversion_factor = preparedItem.active_conversion_factor || 1;

    // Set audit fields
    preparedItem.creation = new Date();
    preparedItem.modified = new Date();
    preparedItem.modified_by = userId;

    // Set ownership if available
    if (customerId) {
      preparedItem.owner = customerId.toString();
    }

    return preparedItem;
  }

  /**
   * Prepare production quantity data for creation
   */
  static prepareProductionQuantityForCreation(prodData: any, taskId: string, userId: string, customerId?: string): any {
    const preparedProd = { ...prodData };

    // Set required fields
    preparedProd.task_id = taskId;

    // Set default values for other fields
    preparedProd.exp_quantity = preparedProd.exp_quantity || 0;
    preparedProd.lost_quantity = preparedProd.lost_quantity || 0;
    preparedProd.finished_quantity = preparedProd.finished_quantity || 0;
    preparedProd.issued_quantity = preparedProd.issued_quantity || 0;
    preparedProd.total_qty_in_crop = preparedProd.total_qty_in_crop || 0;
    preparedProd.draft_quantity = preparedProd.draft_quantity || 0;
    preparedProd.active_conversion_factor = preparedProd.active_conversion_factor || 1;

    // Set audit fields
    preparedProd.creation = new Date();
    preparedProd.modified = new Date();
    preparedProd.modified_by = userId;

    // Set ownership if available
    if (customerId) {
      preparedProd.owner = customerId.toString();
    }

    return preparedProd;
  }

  /**
   * Prepare task transfer data for creation
   */
  static prepareTaskTransferForCreation(transferData: any, targetTaskId: string, userId: string, customerId?: string): any {
    const preparedTransfer = { ...transferData };

    // Set required fields
    preparedTransfer.target_task_id = targetTaskId;
    preparedTransfer.item_id = preparedTransfer.item_id || 'PLACEHOLDER_ITEM';
    preparedTransfer.quantity = preparedTransfer.quantity || 0;
    preparedTransfer.conversion_factor = preparedTransfer.conversion_factor || 1;
    preparedTransfer.transfer_date = preparedTransfer.transfer_date || new Date();
    preparedTransfer.transfer_by = userId;
    preparedTransfer.status = preparedTransfer.status || 'Linked';

    // Set description if not provided
    if (!preparedTransfer.description) {
      preparedTransfer.description = `Task link from ${transferData.source_task_type} to ENV_POUR`;
    }

    // Set audit fields
    preparedTransfer.creation = new Date();
    preparedTransfer.modified = new Date();
    preparedTransfer.modified_by = userId;

    // Set ownership if available
    if (customerId) {
      preparedTransfer.owner = customerId.toString();
    }

    return preparedTransfer;
  }

  /**
   * Extract specific data from update payload
   */
  static extractDataFromUpdatePayload(updateData: any): {
    itemList?: any[];
    prodQuantityList?: any[];
    taskTransfers?: any[];
    cleanTaskData: any;
  } {
    const { item_list, prod_quantity_list, task_transfers, ...cleanTaskData } = updateData;

    // Clean the task data
    this.cleanUpdateDataObject(cleanTaskData);

    return {
      itemList: item_list,
      prodQuantityList: prod_quantity_list,
      taskTransfers: task_transfers,
      cleanTaskData
    };
  }

  /**
   * Format date for database storage
   */
  static formatDateForDatabase(date: Date | string): string {
    if (date instanceof Date) {
      // Use local timezone instead of UTC to preserve the original date
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    return date;
  }

  /**
   * Format date for API response
   */
  static formatDateForResponse(date: Date | string): string {
    if (date instanceof Date) {
      return date.toISOString().replace('T', ' ').substring(0, 19);
    }
    return new Date(date).toISOString().replace('T', ' ').substring(0, 19);
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    if (!input) return '';
    return input.trim().replace(/[<>]/g, '');
  }

  /**
   * Validate and sanitize numeric input
   */
  static sanitizeNumber(input: any, defaultValue: number = 0): number {
    const num = parseFloat(input);
    return isNaN(num) ? defaultValue : Math.max(0, num);
  }

  /**
   * Remove undefined and null values from object
   */
  static removeEmptyValues(obj: any): any {
    const cleaned: any = {};

    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined && value !== null) {
        cleaned[key] = value;
      }
    }

    return cleaned;
  }

  /**
   * Deep clone object to avoid reference issues
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.deepClone(item)) as unknown as T;
    }

    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }

    return cloned;
  }
}
